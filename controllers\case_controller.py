"""
用例控制器
"""
import os
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QDialog, QMessageBox
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal

from views.case_panel import CasePanel
from models.case_model import CaseModel
from utils.case_parser import CaseParser
from utils.event_bus import EventBus

class CaseController(QObject):
    """用例控制器，负责管理用例相关操作"""

    # 定义信号
    case_selected = pyqtSignal(str)  # case_name

    def __init__(self, main_window, config_model):
        """
        初始化用例控制器

        Args:
            main_window (MainWindow): 主窗口
            config_model (ConfigModel): 配置模型
        """
        super().__init__()
        self.main_window = main_window
        self.config_model = config_model
        self.case_model = CaseModel()

        # 获取事件总线实例
        self.event_bus = EventBus.instance()

        # 创建用例面板
        self.case_panel = CasePanel()

        # 设置为左侧面板
        self.main_window.set_left_panel(self.case_panel)

        # 连接信号
        self.connect_signals()

        # 尝试导入缓存管理器
        try:
            from utils.cache_manager import CacheManager
            self.cache_manager = CacheManager()
        except ImportError:
            self.cache_manager = None

    def connect_signals(self):
        """连接信号和槽"""
        # 用例面板信号
        self.case_panel.load_case_file_requested.connect(self.load_case_file)
        self.case_panel.remove_case_file_requested.connect(self.remove_case_file)
        self.case_panel.parse_case_files_requested.connect(self.parse_case_files)
        self.case_panel.case_selected.connect(self.on_case_selected)
        self.case_panel.show_env_parse_dialog_requested.connect(self.show_env_parse_dialog)
        self.case_panel.parse_params_requested.connect(self.parse_params_from_selected)

        # 用例模型信号
        self.case_model.case_files_changed.connect(self.update_config_case_files)
        self.case_model.case_data_changed.connect(self.update_case_tree)

    def on_config_loaded(self, config):
        """
        配置加载后的处理

        Args:
            config (dict): 配置数据
        """
        # 设置用例文件
        if "case_files" in config:
            # 过滤掉不存在的文件
            valid_files = []
            for file_path in config["case_files"]:
                if os.path.exists(file_path):
                    valid_files.append(file_path)
                else:
                    print(f"警告: 用例文件不存在: {file_path}")

            if valid_files:
                self.case_model.set_case_files(valid_files)
                self.parse_case_files()

    @pyqtSlot(str)
    def load_case_file(self, file_path):
        """
        加载用例文件

        Args:
            file_path (str): 文件路径
        """
        if not os.path.exists(file_path):
            self.main_window.show_error("文件错误", f"文件不存在: {file_path}")
            return

        self.case_model.add_case_file(file_path)
        self.parse_case_files()

        # 尝试自动解析 base 和 block 参数
        self.auto_parse_params(file_path)

    @pyqtSlot(str)
    def remove_case_file(self, file_name):
        """
        移除用例文件

        Args:
            file_name (str): 文件名
        """
        # 查找完整路径
        for file_path in self.case_model.get_case_files():
            if os.path.basename(file_path) == file_name:
                self.case_model.remove_case_file(file_path, self.cache_manager)
                self.parse_case_files()
                return

    @pyqtSlot()
    def parse_case_files(self):
        """解析用例文件"""
        # 在解析前先验证和清理无效文件
        self.case_model.validate_and_clean_files(self.cache_manager)
        self.case_model.parse_case_files(self.cache_manager)

    @pyqtSlot(list)
    def update_case_tree(self, case_data):
        """
        更新用例树

        Args:
            case_data (list): 用例数据
        """
        self.case_panel.update_case_tree(case_data)

    @pyqtSlot(list)
    def update_config_case_files(self, case_files):
        """
        更新配置中的用例文件列表

        Args:
            case_files (list): 用例文件列表
        """
        self.config_model.update_config({"case_files": case_files})

    @pyqtSlot(str)
    def on_case_selected(self, case_name):
        """
        处理用例选择事件

        Args:
            case_name (str): 用例名称
        """
        # 如果是多选提示，不处理
        if case_name.startswith("已选择"):
            # 使用事件总线发射用例选择信号
            self.event_bus.emit_case_selected(case_name)
            # 保持向后兼容
            self.case_selected.emit(case_name)
            return

        # 查找选中用例所在的文件（优先使用当前存在的文件）
        selected_file_path = None
        candidate_files = []

        # 首先收集所有包含该用例的文件
        for file_data in self.case_model.get_case_data():
            # 检查用例是否在这个文件中
            if case_name in file_data['nodes'] or any(case == case_name for case, _ in file_data['child_cases']):
                # 优先使用完整路径信息（如果可用）
                if 'full_path' in file_data:
                    candidate_files.append(file_data['full_path'])
                else:
                    # 向后兼容：如果没有完整路径信息，使用原来的匹配逻辑
                    file_name = file_data['name']
                    for file_path in self.case_model.get_case_files():
                        if os.path.basename(file_path) == file_name:
                            candidate_files.append(file_path)
                            break
        
        # 优先选择当前存在的文件
        for file_path in candidate_files:
            if os.path.exists(file_path):
                selected_file_path = file_path
                break
        
        # 如果没有找到存在的文件，使用第一个候选文件（向后兼容）
        if not selected_file_path and candidate_files:
            selected_file_path = candidate_files[0]
            print(f"警告: 用例 {case_name} 对应的文件 {selected_file_path} 不存在，可能需要重新解析用例文件")

        # 如果找到了文件路径，解析base和block参数
        base, block = None, None
        if selected_file_path:
            base, block = CaseParser.parse_base_block_from_path(selected_file_path)

        # 不管是否找到文件路径，先确保case名称被更新到配置中
        config_update = {"case": case_name}
        
        # 只有在成功解析时才更新BASE和BLOCK
        if base is not None and block is not None:
            config_update["base"] = base
            config_update["block"] = block
            
            # 显示提示信息
            self.main_window.show_message(f"已自动设置 BASE={base}, BLOCK={block}")
        
        # 更新配置
        self.config_model.config.update(config_update)
        
        # 通过事件总线通知配置变更
        self.event_bus.emit_config_changed(config_update)
        
        # 通过事件总线发射用例选择信号
        self.event_bus.emit_case_selected(case_name)
        
        # 保持向后兼容
        self.case_selected.emit(case_name)

    def show_env_parse_dialog(self):
        """显示环境解析对话框"""
        from PyQt5.QtWidgets import (
            QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel,
            QLineEdit, QPushButton, QTreeWidget, QTreeWidgetItem,
            QMessageBox, QAbstractItemView
        )
        from PyQt5.QtCore import Qt, QMetaObject, Q_ARG
        from utils.progress_window import ProgressWindow

        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("解析环境")
        dialog.setMinimumWidth(600)

        layout = QVBoxLayout()

        # 获取环境变量并显示路径
        proj_env = os.getenv('PROJ_ENV', '')

        # 如果获取失败，尝试其他方法
        if not proj_env:
            try:
                # 尝试直接从 os.environ 获取
                proj_env = os.environ.get('PROJ_ENV', '')
            except:
                pass

        # 如果仍然获取失败，尝试通过subprocess获取
        if not proj_env:
            try:
                import subprocess
                result = subprocess.run(['printenv', 'PROJ_ENV'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    proj_env = result.stdout.strip()
            except:
                pass

        if not proj_env:
            QMessageBox.warning(self.main_window, "警告", "未找到环境变量 $PROJ_ENV，请先设置环境变量")
            return

        # 添加环境路径显示，使用美化的框架显示
        env_frame = QGroupBox("环境信息")
        env_frame_layout = QVBoxLayout()
        env_label = QLabel(f"环境根目录: {proj_env}")
        env_label.setWordWrap(True)
        env_label.setStyleSheet("color: #444; padding: 5px;")
        env_frame_layout.addWidget(env_label)
        env_frame.setLayout(env_frame_layout)
        layout.addWidget(env_frame)

        # 扫描所有子系统目录
        subsystems = []
        try:
            for item in os.listdir(proj_env):
                full_path = os.path.join(proj_env, item)
                if os.path.isdir(full_path) and (item.endswith('_sys') or item == 'top'):
                    subsystems.append(item)

            if not subsystems:
                QMessageBox.warning(self.main_window, "警告", "未找到任何有效的子系统目录")
                return

        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"扫描目录失败: {str(e)}")
            return

        # 创建子系统选择组
        subsys_group = QGroupBox("可用子系统")
        subsys_layout = QVBoxLayout()

        # 添加搜索过滤功能
        filter_layout = QHBoxLayout()
        filter_label = QLabel("🔍")
        filter_input = QLineEdit()
        filter_input.setPlaceholderText("输入关键字过滤子系统...")
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(filter_input)
        subsys_layout.addLayout(filter_layout)

        # 创建子系统选择列表
        subsys_list = QTreeWidget()
        subsys_list.setHeaderLabels(["子系统"])
        subsys_list.setSelectionMode(QTreeWidget.MultiSelection)
        subsys_list.setAlternatingRowColors(True)
        subsys_list.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
            }
            QTreeWidget::item {
                height: 25px;
                padding: 2px;
            }
            QTreeWidget::item:selected {
                background-color: #e6f3ff;
                color: #000;
            }
            QTreeWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)

        # 添加子系统到列表
        system_items = {}
        for subsys in sorted(subsystems):
            item = QTreeWidgetItem([subsys])
            item.setToolTip(0, f"目录: {os.path.join(proj_env, subsys)}")
            subsys_list.addTopLevelItem(item)
            system_items[subsys] = item

        subsys_layout.addWidget(subsys_list)

        # 添加选择工具按钮
        tool_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        clear_all_btn = QPushButton("清空选择")
        tool_layout.addWidget(select_all_btn)
        tool_layout.addWidget(clear_all_btn)
        subsys_layout.addLayout(tool_layout)

        # 添加统计信息标签
        stats_label = QLabel(f"找到 {len(subsystems)} 个子系统")
        stats_label.setStyleSheet("color: #666; margin-top: 5px;")
        subsys_layout.addWidget(stats_label)

        subsys_group.setLayout(subsys_layout)
        layout.addWidget(subsys_group)

        # 按钮区域
        btn_layout = QHBoxLayout()
        parse_btn = QPushButton("解析")
        parse_btn.setDefault(True)
        parse_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a9eff;
                color: white;
                padding: 6px 20px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3d8ced;
            }
            QPushButton:pressed {
                background-color: #3274bf;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        cancel_btn = QPushButton("取消")

        # 根据选择状态启用/禁用解析按钮
        parse_btn.setEnabled(False)

        def on_selection_changed():
            selected_count = len(subsys_list.selectedItems())
            parse_btn.setEnabled(selected_count > 0)
            stats_label.setText(
                f"找到 {len(subsystems)} 个子系统，已选择 {selected_count} 个"
            )

        def on_filter_changed(text):
            filter_text = text.lower()
            for subsys, item in system_items.items():
                item.setHidden(not subsys.lower().__contains__(filter_text))

        def select_all():
            subsys_list.selectAll()

        def clear_selection():
            subsys_list.clearSelection()

        # 连接信号
        subsys_list.itemSelectionChanged.connect(on_selection_changed)
        filter_input.textChanged.connect(on_filter_changed)
        select_all_btn.clicked.connect(select_all)
        clear_all_btn.clicked.connect(clear_selection)

        btn_layout.addWidget(parse_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        dialog.setLayout(layout)

        # 连接信号
        parse_btn.clicked.connect(lambda: self.parse_selected_subsystems(subsys_list, dialog, proj_env))
        cancel_btn.clicked.connect(dialog.reject)

        # 设置默认大小并显示
        dialog.resize(800, 600)
        dialog.exec_()

    def parse_selected_subsystems(self, subsys_list, dialog, proj_env):
        """解析选中的子系统，生成用例树"""
        from PyQt5.QtWidgets import QMessageBox
        from PyQt5.QtCore import QMetaObject, Qt, Q_ARG
        from utils.progress_window import ProgressWindow

        selected_items = subsys_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(dialog, "警告", "请选择至少一个子系统")
            return

        # 清空原有的用例文件列表
        self.case_model.clear_case_files()

        # 创建进度窗口
        progress = ProgressWindow(self.main_window, "解析子系统", "正在解析环境配置文件...")
        progress.progress.setMinimum(0)
        progress.progress.setMaximum(100)

        try:
            total_subsys = len(selected_items)
            found_files = 0
            skipped_files = 0

            for i, item in enumerate(selected_items):
                subsys = item.text(0)
                progress.progress.setLabelText(f"正在分析 {subsys} ...")
                progress_value = (i + 1) * 100 // total_subsys
                QMetaObject.invokeMethod(
                    progress.progress,
                    "setValue",
                    Qt.ConnectionType.QueuedConnection,
                    Q_ARG(int, progress_value)
                )

                # 处理子系统目录下的配置文件
                subsys_cfg_path = os.path.join(proj_env, subsys, 'bin', 'case_cfg')
                if os.path.exists(subsys_cfg_path):
                    try:
                        for cfg in os.listdir(subsys_cfg_path):
                            if cfg.endswith('.cfg'):
                                cfg_path = os.path.join(subsys_cfg_path, cfg)
                                if cfg_path not in self.case_model.get_case_files():
                                    self.case_model.add_case_file(cfg_path)
                                    found_files += 1
                                else:
                                    skipped_files += 1
                    except Exception as e:
                        print(f"警告: 处理目录 {subsys_cfg_path} 时出错: {str(e)}")

                # 处理 udtb 目录，让用户选择要解析的子目录
                udtb_dirs = self.show_udtb_selection_dialog(subsys, proj_env)
                for udtb_dir in udtb_dirs:
                    try:
                        # 只在选定的目录中查找 .cfg 文件，不再递归查找
                        bin_dir = os.path.join(udtb_dir, 'bin')
                        if os.path.exists(bin_dir):
                            for file in os.listdir(bin_dir):
                                if file.endswith('.cfg'):
                                    cfg_path = os.path.join(bin_dir, file)
                                    if cfg_path not in self.case_model.get_case_files():
                                        self.case_model.add_case_file(cfg_path)
                                        found_files += 1
                                    else:
                                        skipped_files += 1
                    except Exception as e:
                        print(f"警告: 处理 UDTB 目录 {udtb_dir} 时出错: {str(e)}")

                # 特殊处理 usvp 目录下的配置文件
                if subsys in ['apcpu_sys', 'aon_sys', 'ch_sys', 'sp_sys', 'phy_cp_sys', 'ps_cp_sys']:
                    usvp_cfg_path = os.path.join(proj_env, 'udtb', 'usvp', 'bin', 'case_cfg')
                    if os.path.exists(usvp_cfg_path):
                        # 定义每个子系统需要解析的特定配置文件
                        cfg_patterns = {
                            'apcpu_sys': ['apcpu_subsys_case.cfg', 'apcpu_top_case.cfg'],
                            'aon_sys': ['ch_subsys_case.cfg', 'ch_top_case.cfg',
                                       'sp_subsys_case.cfg', 'sp_top_case.cfg'],
                            'ch_sys': ['ch_subsys_case.cfg', 'ch_top_case.cfg'],
                            'sp_sys': ['sp_subsys_case.cfg', 'sp_top_case.cfg'],
                            'phy_cp_sys': ['phycp_subsys_case.cfg', 'phycp_top_case.cfg'],
                            'ps_cp_sys': ['pscp_subsys_case.cfg', 'pscp_top_case.cfg']
                        }

                        try:
                            # 添加匹配的配置文件
                            for pattern in cfg_patterns.get(subsys, []):
                                cfg_path = os.path.join(usvp_cfg_path, pattern)
                                if os.path.exists(cfg_path):
                                    if cfg_path not in self.case_model.get_case_files():
                                        self.case_model.add_case_file(cfg_path)
                                        found_files += 1
                                    else:
                                        skipped_files += 1
                        except Exception as e:
                            print(f"警告: 处理 USVP 配置文件时出错: {str(e)}")

            if not self.case_model.get_case_files():
                QMessageBox.warning(dialog, "警告", "未找到任何配置文件！")
                return

            # 解析完成后更新用例树
            self.parse_case_files()
            dialog.accept()

            # 更新状态栏，显示详细信息
            status_msg = f"环境解析完成: 发现 {found_files} 个配置文件"
            if skipped_files > 0:
                status_msg += f", 跳过 {skipped_files} 个重复文件"
            self.main_window.show_message(status_msg, 5000)

        except Exception as e:
            QMessageBox.critical(dialog, "错误", f"解析子系统失败: {str(e)}")
        finally:
            progress.close()

    def show_udtb_selection_dialog(self, subsys, proj_env):
        """显示 UDTB 目录选择对话框"""
        from PyQt5.QtWidgets import (
            QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
            QPushButton, QListWidget, QListWidgetItem, QAbstractItemView,
            QDialogButtonBox, QMessageBox
        )

        udtb_path = os.path.join(proj_env, 'udtb', subsys)
        if not os.path.exists(udtb_path):
            return []

        dialog = QDialog(self.main_window)
        dialog.setWindowTitle(f"选择 {subsys} UDTB 目录")
        dialog.setMinimumWidth(600)

        layout = QVBoxLayout()

        # 添加说明标签
        hint_label = QLabel(f"选择 {subsys} 的 UDTB 目录用于解析配置文件:")
        hint_label.setStyleSheet("color: #444; margin-bottom: 10px;")
        layout.addWidget(hint_label)

        # 添加搜索过滤功能
        filter_layout = QHBoxLayout()
        filter_label = QLabel("🔍")
        filter_input = QLineEdit()
        filter_input.setPlaceholderText("输入关键字过滤目录...")
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(filter_input)
        layout.addLayout(filter_layout)

        # 创建目录列表
        list_widget = QListWidget()
        list_widget.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        list_widget.setAlternatingRowColors(True)
        list_widget.setStyleSheet("""
            QListWidget {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
            }
            QListWidget::item {
                height: 25px;
                padding: 2px 5px;
            }
            QListWidget::item:selected {
                background-color: #e6f3ff;
                color: #000;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)

        # 存储所有列表项以便过滤
        list_items = {}

        # 获取所有包含 bin 目录的子目录
        for root, dirs, _ in os.walk(udtb_path):
            if 'bin' in dirs:
                # 获取相对路径
                rel_path = os.path.relpath(root, udtb_path)
                if rel_path != '.':  # 排除当前目录
                    item = QListWidgetItem(rel_path)
                    item.setToolTip(f"完整路径: {root}")
                    list_widget.addItem(item)
                    list_items[rel_path] = item

        layout.addWidget(list_widget)

        # 添加按钮行
        btn_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        clear_btn = QPushButton("清空选择")
        btn_layout.addWidget(select_all_btn)
        btn_layout.addWidget(clear_btn)
        layout.addLayout(btn_layout)

        # 添加统计标签
        stats_label = QLabel(f"共找到 {len(list_items)} 个目录")
        stats_label.setStyleSheet("color: #666; margin-top: 5px;")
        layout.addWidget(stats_label)

        # 添加确定取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        ok_button = button_box.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText("确定")
        cancel_button = button_box.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText("取消")

        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # 实现过滤功能
        def filter_items(text):
            filter_text = text.lower()
            for path, item in list_items.items():
                item.setHidden(not path.lower().__contains__(filter_text))

        def update_stats():
            selected_count = len(list_widget.selectedItems())
            visible_count = sum(1 for item in list_items.values() if not item.isHidden())
            if selected_count > 0:
                stats_label.setText(f"共 {len(list_items)} 个目录，显示 {visible_count} 个，已选择 {selected_count} 个")
            else:
                stats_label.setText(f"共 {len(list_items)} 个目录，显示 {visible_count} 个")

        def select_all():
            list_widget.selectAll()
            update_stats()

        def clear_selection():
            list_widget.clearSelection()
            update_stats()

        # 连接信号
        filter_input.textChanged.connect(filter_items)
        select_all_btn.clicked.connect(select_all)
        clear_btn.clicked.connect(clear_selection)
        list_widget.itemSelectionChanged.connect(update_stats)
        filter_input.textChanged.connect(update_stats)

        # 设置对话框默认大小
        dialog.resize(800, 600)
        dialog.setLayout(layout)

        # 获取选择结果
        selected_dirs = []
        if dialog.exec() == QDialog.DialogCode.Accepted:
            selected_items = list_widget.selectedItems()
            if not selected_items:
                if QMessageBox.question(
                    dialog,
                    "确认",
                    "您没有选择任何目录，是否继续？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                ) == QMessageBox.No:
                    return self.show_udtb_selection_dialog(subsys, proj_env)

            for item in selected_items:
                selected_dir = os.path.join(udtb_path, item.text())
                selected_dirs.append(selected_dir)

        return selected_dirs

    @pyqtSlot(list)
    def parse_params_from_selected(self, items):
        """
        从选中的用例文件解析 Base/Block 参数

        Args:
            items (list): 选中的树节点项列表
        """
        if not items:
            return

        # 获取第一个选中项对应的文件路径
        file_name = items[0].text(0)
        selected_file_path = None

        # 查找文件路径
        for file_path in self.case_model.get_case_files():
            if os.path.basename(file_path) == file_name:
                selected_file_path = file_path
                break

        if selected_file_path:
            base, block = CaseParser.parse_base_block_from_path(selected_file_path)

            if base is not None and block is not None:
                # 更新配置
                config_update = {
                    "base": base,
                    "block": block
                }

                # 直接更新配置模型
                self.config_model.config.update(config_update)

                # 显示提示信息
                self.main_window.show_message(f"已自动设置 BASE={base}, BLOCK={block}")

                # 通过事件总线通知配置变更
                self.event_bus.emit_config_changed(config_update)
            else:
                self.main_window.show_warning("解析失败", "无法从选中的文件解析 BASE/BLOCK 参数")
        else:
            self.main_window.show_warning("文件错误", "找不到选中的文件")

    def auto_parse_params(self, file_path):
        """
        自动解析 base 和 block 参数

        Args:
            file_path (str): 文件路径
        """
        base, block = CaseParser.parse_base_block_from_path(file_path)

        if base is not None and block is not None:
            # 获取当前配置
            config = self.config_model.get_config()

            # 如果当前没有设置 base 和 block，则自动设置
            if not config.get("base") and not config.get("block"):
                # 更新配置
                config_update = {
                    "base": base,
                    "block": block
                }

                # 直接更新配置模型
                self.config_model.config.update(config_update)

                # 显示提示信息
                self.main_window.show_message(f"已自动设置 BASE={base}, BLOCK={block}")

                # 通过事件总线通知配置变更
                self.event_bus.emit_config_changed(config_update)
